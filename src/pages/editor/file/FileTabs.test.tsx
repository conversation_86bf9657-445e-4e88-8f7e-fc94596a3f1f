import { FileTabs } from '@/pages/editor/file/FileTabs';
import { render, screen } from '@/test-utils';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { ReactNode, useEffect } from 'react';
import { ActiveFile, FileStore } from '@/utils/graphEditor/data';

const defaultProps = {
  onRunAll: vi.fn(),
  onRunSelection: vi.fn(),
  onCreateFile: vi.fn(),
  onChangeFile: vi.fn(),
  onSaveAll: vi.fn(),
  updateFileStore: vi.fn(),
  onDeleteFile: vi.fn(),
  onOpenShareDrawer: vi.fn(),
  onMoveFile: vi.fn(),
  userFolders: [] as FileStore[],
  currentSavingId: null,
  setCurrentSavingId: vi.fn(),
  isSavingAll: false,
};

function WrapperWithoutActiveFiles({ children }: { children: ReactNode }) {
  const { setActiveFiles, setCurrentFileId } = useEditorContext();
  useEffect(() => {
    setActiveFiles([]);
    setCurrentFileId('');
  }, [setActiveFiles, setCurrentFileId]);

  return children;
}

describe('No active file', () => {
  test('render placeholder', async () => {
    render(
      <WrapperWithoutActiveFiles>
        <FileTabs {...defaultProps} />
      </WrapperWithoutActiveFiles>
    );

    expect(await screen.findByText('Create New GSQL File')).toBeInTheDocument();
  });

  test('create new file', async () => {
    render(
      <WrapperWithoutActiveFiles>
        <FileTabs {...defaultProps} />
      </WrapperWithoutActiveFiles>
    );

    const createButton = await screen.findByText('Create New GSQL File');
    createButton.click();
    expect(defaultProps.onCreateFile).toHaveBeenCalled();
  });
});

function WrapperWithActiveFiles() {
  const { setActiveFiles, setCurrentFileId } = useEditorContext();
  useEffect(() => {
    setActiveFiles([
      { id: '1', type: 'UserFile', name: 'test1.gsql', content: 'content1', sourceCode: 'content1' } as ActiveFile,
      { id: '2', type: 'UserFile', name: 'test2.gsql', content: 'content2', sourceCode: 'content2' } as ActiveFile,
    ]);
    setCurrentFileId('1');
  }, [setActiveFiles, setCurrentFileId]);

  return <FileTabs {...defaultProps} />;
}

describe('Has multiple active files', () => {
  test('render file tabs', async () => {
    render(<WrapperWithActiveFiles />);
    expect(await screen.findByText('test1.gsql')).toBeInTheDocument();
    expect(await screen.findByText('test2.gsql')).toBeInTheDocument();
    expect(await screen.findByText('content1')).toBeInTheDocument();
    expect(screen.queryByText('content2')).not.toBeInTheDocument();
  });

  test('close active file', async () => {
    render(<WrapperWithActiveFiles />);
    const closeButton = await screen.findAllByTestId('close-file-tab');
    closeButton[0].click();
    expect(await screen.findByText('test2.gsql')).toBeInTheDocument();
    expect(await screen.findByText('content2')).toBeInTheDocument();
    expect(screen.queryByText('test1.gsql')).not.toBeInTheDocument();
  });

  test('prev/next file tab', async () => {
    render(<WrapperWithActiveFiles />);
    const nextButton = await screen.findByTestId('next-file-tab');
    nextButton.click();
    expect(await screen.findByText('test2.gsql')).toBeInTheDocument();
    expect(await screen.findByText('content2')).toBeInTheDocument();
    const prevButton = await screen.findByTestId('prev-file-tab');
    prevButton.click();
    expect(await screen.findByText('test1.gsql')).toBeInTheDocument();
    expect(await screen.findByText('content1')).toBeInTheDocument();
  });
});

function WrapperWithSourceCodeUndefined() {
  const { setActiveFiles, setCurrentFileId } = useEditorContext();
  useEffect(() => {
    setActiveFiles([
      { id: '1', type: 'UserFile', name: 'test1.gsql', content: 'content1', sourceCode: undefined } as ActiveFile,
    ]);
    setCurrentFileId('1');
  }, [setActiveFiles, setCurrentFileId]);

  return <FileTabs {...defaultProps} />;
}

describe('file source code is undefined', () => {
  test('will fetch file content', async () => {
    render(<WrapperWithSourceCodeUndefined />);
    const fileContent = await screen.findByText('content1');
    expect(fileContent).toBeInTheDocument();
  });
});
