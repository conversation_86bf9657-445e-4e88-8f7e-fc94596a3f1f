import React from 'react';
import { FileStore, FilePermission } from '@/utils/graphEditor/data';
import { MoveIcon, FolderIcon } from '@/pages/home/<USER>';
import { Button } from '@tigergraph/app-ui-lib/button';
import { BsThreeDots } from 'react-icons/bs';
import { useTheme } from '@/contexts/themeContext';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import clsx from 'clsx';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';

export interface FileAction {
  label: string;
  handleFn?: Function;
  disabled: boolean;
  hidden: boolean;
  icon: React.ReactElement;
}

interface FileActionsDropdownProps {
  file: FileStore;
  displayActions: FileAction[];
  userFolders: FileStore[];
  onFileMenuOpen?: (isOpen: boolean) => void;
  onMoveFile: (fromId: string, parentFileId: string | null) => void;
  isShow?: boolean;
  triggerIcon?: React.ReactElement;
}

function FileInfo({ file }: { file: FileStore }) {
  const [, theme] = useStyletron();
  const role =
    file.permission === FilePermission.Owner ? 'Owner' : file.permission === FilePermission.Edit ? 'Editor' : 'Viewer';

  const info = [
    { label: 'Owner', value: file.owner_email },
    { label: 'Your Role', value: role },
    { label: 'Created At', value: new Date(file.created_at).toLocaleString() },
    { label: 'Updated At', value: new Date(file.updated_at).toLocaleString() },
  ];

  return (
    <div className="p-2">
      {info.map((item, index) => (
        <div className={clsx(index !== info.length - 1 && 'mb-2')} key={item.label}>
          <div
            style={{
              color: theme.colors['text.secondary'],
              ...theme.typography.Body2,
            }}
          >
            {item.label}:
          </div>
          <div
            style={{
              color: theme.colors['text.primary'],
              ...theme.typography.Body2,
            }}
          >
            {item.value}
          </div>
        </div>
      ))}
    </div>
  );
}

function FileItemAction({ action, file }: { action: FileAction; file: FileStore }) {
  const [, theme] = useStyletron();

  if (action.label === 'File Info') {
    return (
      <DropdownMenuSub>
        <DropdownMenuSubTrigger disabled={action.disabled}>
          <span>{action.icon}</span>
          <span className="ml-2">{action.label}</span>
        </DropdownMenuSubTrigger>
        <DropdownMenuSubContent>
          <FileInfo file={file} />
        </DropdownMenuSubContent>
      </DropdownMenuSub>
    );
  }

  return (
    <DropdownMenuItem
      disabled={action.disabled}
      onSelect={(event) => {
        action.handleFn?.();
        event.stopPropagation();
      }}
      onClick={(event) => {
        event.stopPropagation();
      }}
    >
      <span>{action.icon}</span>
      <span
        style={{
          color: action.label === 'Delete' ? theme.colors['dropdown.text.error'] : 'inherit',
        }}
      >
        {action.label}
      </span>
    </DropdownMenuItem>
  );
}

export function FileActionsDropdown({
  file,
  displayActions,
  userFolders,
  onFileMenuOpen,
  onMoveFile,
  isShow = true,
  triggerIcon,
}: FileActionsDropdownProps) {
  const { theme } = useTheme();

  const isOwner = file.permission === FilePermission.Owner;
  const showMoveFile = (file: FileStore) => {
    return !file.is_folder && (userFolders.length !== 0 || !!file.parent_id) && isOwner;
  };

  return (
    <div className={clsx(!isShow && 'invisible')}>
      <DropdownMenu
        onOpenChange={(open) => {
          onFileMenuOpen?.(open);
        }}
      >
        <DropdownMenuTrigger asChild>
          <Button
            size="compact"
            kind="text"
            shape="square"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {triggerIcon || <BsThreeDots title="actions" size={20} color={theme.colors['icon.primary']} />}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-26">
          {displayActions.map((action) => (
            <FileItemAction key={action.label} action={action} file={file} />
          ))}
          {showMoveFile(file) && (
            <DropdownMenuSub>
              <DropdownMenuSubTrigger disabled={!isOwner}>
                <span className={clsx(!isOwner && 'opacity-50')}>
                  <MoveIcon />
                </span>
                <span className="ml-2">Move</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {isOwner ? (
                  <>
                    {userFolders.length > 0 && (
                      <>
                        <DropdownMenuLabel>Folders</DropdownMenuLabel>
                        {userFolders.map((folder) => (
                          <DropdownMenuItem
                            key={folder.id}
                            onSelect={() => {
                              onMoveFile(file.is_temp ? file.file_id : file.id, folder.id);
                            }}
                            onClick={(event) => {
                              event.stopPropagation();
                            }}
                          >
                            <FolderIcon />
                            <span>{folder.name}</span>
                          </DropdownMenuItem>
                        ))}
                      </>
                    )}
                    {file.parent_id && (
                      <>
                        {userFolders.length > 0 && <DropdownMenuSeparator />}
                        <DropdownMenuItem
                          onSelect={() => {
                            onMoveFile(file.is_temp ? file.file_id : file.id, null);
                          }}
                          onClick={(event) => {
                            event.stopPropagation();
                          }}
                        >
                          Remove from folder
                        </DropdownMenuItem>
                      </>
                    )}
                  </>
                ) : (
                  <DropdownMenuItem disabled>You are not the owner of this file</DropdownMenuItem>
                )}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
