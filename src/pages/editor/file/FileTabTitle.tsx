import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { MouseEvent, useRef, useState, useEffect, KeyboardEvent } from 'react';
import { ActiveFile, FileStore, FilePermission, FileChangeType } from '@/utils/graphEditor/data';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { IoClose } from 'react-icons/io5';
import { FaSave } from 'react-icons/fa';
import clsx from 'clsx';
import { useHover } from 'ahooks';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Input } from '@tigergraph/app-ui-lib/input';
import { FileActionsDropdown, FileAction } from '@/pages/editor/file/FileActionsDropdown';
import { DeleteIcon, ShareIcon, RenameIcon } from '@/pages/home/<USER>';
import { useMutation } from 'react-query';
import { renameFileReq } from '@/pages/editor/file/api';
import { showToast } from '@/components/styledToasterContainer';
import { getErrorMessage } from '@/utils/utils';
import { AxiosError } from 'axios';
import { expand } from 'inline-style-expand-shorthand';
import { BsThreeDotsVertical } from 'react-icons/bs';

interface FileTabTitleProps {
  file: ActiveFile;
  index: number;
  currentFileId: string;
  unsavedFiles: ActiveFile[];
  currentSavingId: string | null;
  isSavingAll: boolean;
  onSaveFile: (file: ActiveFile, content: string) => void;
  onCloseTab: (fileId: string) => void;
  onShowConfirmDialog: (fileId: string) => void;
  onDeleteFile: (file: FileStore) => void;
  onOpenShareDrawer: (file: FileStore) => void;
  onMoveFile: (fromId: string, parentFileId: string | null) => void;
  onChangeFile: (type: FileChangeType, file: FileStore) => void;
  userFolders: FileStore[];
}

export const FileTabTitle = ({
  file,
  index,
  currentFileId,
  unsavedFiles,
  currentSavingId,
  isSavingAll,
  onSaveFile,
  onCloseTab,
  onShowConfirmDialog,
  onDeleteFile,
  onOpenShareDrawer,
  onMoveFile,
  onChangeFile,
  userFolders = [],
}: FileTabTitleProps) => {
  const [css, theme] = useStyletron();

  const ref = useRef<HTMLDivElement>(null);
  const isHovering = useHover(ref);

  const { name: fileName, id: fileId } = file;
  const isActive = fileId === currentFileId;
  const unsavedFile = unsavedFiles.find((file) => file.id === fileId);
  const unsaved = !!unsavedFile;
  const isSaving = currentSavingId === file.id && !isSavingAll;

  // Rename state and logic
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [tempName, setTempName] = useState<string>(fileName);

  useEffect(() => {
    if (isEditing) {
      setTempName(fileName);

      setTimeout(() => {
        const input = document.querySelector(`input[name="file-name-input-${file.id}"]`) as HTMLInputElement;
        input?.focus();
        input?.select();
      }, 200);
    }
  }, [file.id, fileName, isEditing]);

  const exitEditing = () => {
    setIsEditing(false);
    setTempName(fileName);
  };

  const renameFileClient = useMutation('renameFile', renameFileReq, {
    onSuccess: (data, variables) => {
      exitEditing();
      onChangeFile(FileChangeType.UPDATE, {
        id: file.is_temp ? file.file_id : file.id,
        name: variables.name,
      } as FileStore);
    },
    onError: (error: AxiosError) => {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
      exitEditing();
    },
  });

  const handleRenameFile = (newName: string) => {
    if (newName.trim() === fileName) {
      exitEditing();
      return;
    }
    if (!newName.trim()) {
      showToast({ kind: 'negative', message: 'File name cannot be empty' });
      exitEditing();
      return;
    }

    renameFileClient.mutate({
      fileId: file.is_temp ? file.file_id! : file.id,
      name: newName,
    });
  };

  const isOwner = file.permission === FilePermission.Owner;

  const fileActions: FileAction[] = [
    {
      label: 'Share',
      handleFn: () => {
        if (file.is_temp) {
          onOpenShareDrawer({ ...file, id: file.file_id! });
          return;
        }
        onOpenShareDrawer(file);
      },
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <ShareIcon />,
    },
    {
      label: 'Rename',
      handleFn: () => setIsEditing(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <RenameIcon />,
    },
    {
      label: 'Delete',
      handleFn: () => {
        if (file.is_temp) {
          onDeleteFile({ ...file, id: file.file_id! });
          return;
        }
        onDeleteFile(file);
      },
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <DeleteIcon />,
    },
  ];

  const displayActions = file.is_temp && !file.file_id ? [] : fileActions.filter((action) => !action.hidden);

  const handleCloseClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (!unsaved) {
      onCloseTab(fileId);
      return;
    }
    onShowConfirmDialog(fileId);
  };

  const handleSaveClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();

    if (unsavedFile) {
      onSaveFile(file, unsavedFile.content);
    }
  };

  const handleRenameKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    switch (e.key) {
      case 'Enter':
        handleRenameFile(tempName);
        return;
      case 'Escape':
        exitEditing();
        return;
    }
  };

  return (
    <div className={clsx('flex items-center relative whitespace-nowrap min-w-[50px]')} ref={ref}>
      {isEditing ? (
        <Input
          name={`file-name-input-${file.id}`}
          value={tempName}
          autoFocus
          onChange={(e) => setTempName(e.currentTarget.value)}
          onKeyDown={handleRenameKeyDown}
          onBlur={() => handleRenameFile(tempName)}
          overrides={{
            Root: {
              style: {
                ...expand({ borderWidth: '1px' }),
                height: '20px',
                minWidth: '80px',
              },
            },
            Input: {
              style: {
                fontSize: '12px',
                ...expand({ padding: '0 0 0 4px' }),
              },
            },
          }}
        />
      ) : (
        <span>{fileName}</span>
      )}
      {!isEditing && (unsaved ? <span>*</span> : <span>&nbsp;&nbsp;</span>)}

      <div className="flex items-center gap-[2px] ml-[4px] min-w-[16px]">
        {!isSaving && unsaved && (isHovering || isActive) && (
          <Button
            onClick={handleSaveClick}
            size="compact"
            kind="text"
            shape="square"
            overrides={{ BaseButton: { style: { ...expand({ padding: '0px' }) } } }}
          >
            <FaSave size={14} color={theme.colors['icon.primary']} />
          </Button>
        )}

        {isSaving && <Spinner $color={theme.colors.gray1000} $size={'14px'} $borderWidth={'2px'} />}

        {displayActions.length > 0 && (
          <FileActionsDropdown
            file={file}
            displayActions={displayActions}
            userFolders={userFolders}
            onMoveFile={onMoveFile}
            isShow={isHovering || isActive}
            triggerIcon={<BsThreeDotsVertical size={14} color={theme.colors['icon.primary']} />}
          />
        )}

        {(isHovering || isActive) && (
          <Button
            onClick={handleCloseClick}
            size="compact"
            kind="text"
            shape="square"
            overrides={{ BaseButton: { style: { ...expand({ padding: '0px' }) } } }}
          >
            <IoClose size={16} color={theme.colors['tabs.icon']} />
          </Button>
        )}
      </div>
    </div>
  );
};
