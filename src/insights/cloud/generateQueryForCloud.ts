import { GlobalParams, buildURLSearchParams } from '@tigergraph/tools-ui/esm/insights/chart';
import { ChartState } from '@tigergraph/tools-ui/esm/insights/charts';
import {
  generateQueryURLSearchParams,
  interpolateQuery,
} from '@tigergraph/tools-ui/esm/insights/charts/query/useGSQLQuery';
import { QueryType } from '@/insights/cloud/type';
import { encodeURLSearchParams } from '@tigergraph/tools-ui/esm/insights/utils';
import { QueryMeta } from '@tigergraph/tools-models/query';
import { getQueriesInfo } from '@tigergraph/tools-models';
import { WorkspaceT } from '@/pages/workgroup/type';
import { ID_TOKEN_KEY } from '@/contexts/workspaceContext';

type CloudQuery = {
  graph?: string;
  query_type: QueryType;
  query: string;
  query_params: string;
};

// generate real query from widget configuration, so we can run schedule this query to run on cloud
export async function generateQueryForCloud(
  widget: ChartState,
  globalParameters: GlobalParams,
  queries: { [key: string]: QueryMeta },
  workspace: WorkspaceT
): Promise<CloudQuery> {
  const { query, queryType, searchPattern, graphName: graph } = widget;
  if (queryType === 'interactive') {
    const constructedQuery = interpolateQuery(query, globalParameters);

    const response = await getQueriesInfo(
      {
        queryBody: constructedQuery,
      },
      {
        baseURL: `https://${workspace.nginx_host}`,
        version: workspace.tg_version,
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem(ID_TOKEN_KEY)}`,
        },
      }
    );

    const queryParams = response.data.results.params;
    const { error, urlSearchParams } = buildURLSearchParams(globalParameters, queryParams, true);
    if (error) {
      throw error;
    }
    return {
      graph,
      query_type: 'interpreted',
      query: constructedQuery,
      query_params: encodeURLSearchParams(urlSearchParams),
    };
  } else if (queryType === 'pattern') {
    const queryName = searchPattern[0].data;
    const query = queries[queryName];
    let params = new URLSearchParams();
    generateQueryURLSearchParams(searchPattern, globalParameters, query, params);
    const constructedQuery = queries[queryName].originalCode;
    return {
      graph,
      query_type: query.installed ? 'installed' : 'interpreted',
      query: query.installed ? queryName : constructedQuery,
      query_params: encodeURLSearchParams(params),
    };
  } else {
    return {
      query_type: 'none',
      query: '',
      query_params: '',
    };
  }
}
